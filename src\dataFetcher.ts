import type { D1Database } from '@cloudflare/workers-types';
import { PlatformAccountData, PlatformAccountsCollection, validatePlatformAccountsCollection, updateSinglePlatformAccount } from './database';



interface ToutiaoIncomeResponse {
  code: number;
  message: string;
  data?: Array<{
    is_yesterday_income_ready?: boolean;
    lastday?: number;
    period?: number;
    title: string;
    type: string;
    total?: number;
    settle_info?: {
      settle_abstract?: {
        扣税额?: number;
        税前收益?: number;
        税后收益?: number;
      };
      settle_detail?: {
        [key: string]: number;
      };
      settle_time_range?: {
        range_end?: string;
        range_start?: string;
      };
    };
  }>;
}

// 信用分和粉丝数API响应接口
interface ToutiaoBenefitResponse {
  code: number;
  err_no: number;
  message: string;
  data?: {
    activated_benefit_num: number;
    benefit_level_info_list: Array<{
      benefit_list: Array<{
        apply_type: number;
        credit_score_limit: number;
        describe: string;
        message: string;
        message_url: string;
        name: string;
        status_code: number;
        tag: string;
        type: number;
        url: string;
      }>;
      describe: string;
      fans_num_threshold: number;
      level: number;
      type: number;
    }>;
    creator_project_fans_threshold: number;
    creator_project_info: {
      can_re_join: boolean;
      message: string;
      status: number;
      toast_message: string;
      type: number;
    };
    credit_info: {
      score: number;
      score_change: number;
      stick_id: number;
      stick_id_list: number[];
      tip: string;
    };
    fans_num: number;
    is_low_quality: boolean;
  };
}

// 账号详细信息响应接口
interface ToutiaoAccountInfoResponse {
  html_content?: string;
  content_type?: string;
  user_info?: {
    id?: string;
    screen_name?: string;
    has_already_authentication?: boolean; // 实名认证状态
  };
}

// 禁言状态API响应接口
interface ToutiaoPunishStatusResponse {
  err_no: number;
  err_tips: string;
  data?: {
    tips?: string;
    appeal_status?: number;
    appeal_fail_reason?: any;
    group?: {
      title: string;
      thumb_url: string;
      schema: string;
      has_video: number;
      image_style: number;
      count: number;
      publish_time: number;
      video_duration: number;
      group_source: number;
      reason: string;
    };
    punish_time?: string;
    audit_rule?: string;
    audit_url?: string;
    reason?: string;
    groups?: Array<{
      title: string;
      thumb_url: string;
      schema: string;
      has_video: number;
      publish_time: number;
      video_duration: number;
      group_source: number;
      reason: string;
    }>;
  } | null;
}

// 数据获取结果接口
interface FetchResult {
  success: boolean;
  phone: string;
  message: string;
  isYesterdayIncomeReady?: boolean; // 新增字段，标识昨日收益是否已准备好
  accountOffline?: boolean; // 新增字段，标识账号是否掉线
  data?: {
    total_income?: string;
    yesterday_income?: string;
    can_withdraw_amount?: string; // 可提现金额
    credit_score?: string; // 信用分
    fans_num?: string; // 粉丝数
    username?: string; // 用户名
    homepage_url?: string; // 主页URL
    account_status?: string; // 账号状态（正常/禁言）
    is_verified?: string; // 实名认证状态（已实名/未实名）
  };
}





/**
 * 获取收益数据（带重试机制）
 */
async function fetchIncomeData(sessionid: string): Promise<ToutiaoIncomeResponse> {
  const maxRetries = 3;
  let lastError: Error | null = null;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const response = await fetch('https://mp.toutiao.com/pgc/mp/income/income_statement_abstract?only_mid_income=false&days=30&app_id=1231', {
        method: 'GET',
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0',
          'Accept': 'application/json, text/plain, */*',
          'Referer': 'https://mp.toutiao.com/profile_v4/analysis/income-overview',
          'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
          'Cookie': `sessionid=${sessionid}`
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json() as ToutiaoIncomeResponse;

    } catch (error) {
      lastError = error as Error;

      // 如果不是最后一次尝试，等待后重试
      if (attempt < maxRetries) {
        await new Promise(resolve => setTimeout(resolve, attempt * 2000));
      }
    }
  }

  throw lastError || new Error('获取收益数据失败');
}

/**
 * 获取信用分和粉丝数数据（带重试机制）
 */
async function fetchBenefitData(sessionid: string): Promise<ToutiaoBenefitResponse> {
  const maxRetries = 3;
  let lastError: Error | null = null;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const response = await fetch('https://mp.toutiao.com/mp/agw/creator_project/get_benefit_page_info?app_id=1231', {
        method: 'GET',
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0',
          'Accept': 'application/json, text/plain, */*',
          'Referer': 'https://mp.toutiao.com/profile_v4/analysis/works-overall/all',
          'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
          'Cookie': `sessionid=${sessionid}`
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json() as ToutiaoBenefitResponse;

    } catch (error) {
      lastError = error as Error;

      // 如果不是最后一次尝试，等待后重试
      if (attempt < maxRetries) {
        await new Promise(resolve => setTimeout(resolve, attempt * 2000));
      }
    }
  }

  throw lastError || new Error('获取信用分和粉丝数数据失败');
}

/**
 * 获取账号详细信息（带重试机制）
 */
async function fetchAccountInfo(sessionid: string): Promise<ToutiaoAccountInfoResponse> {
  const maxRetries = 3;
  let lastError: Error | null = null;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const response = await fetch('https://mp.toutiao.com/profile_v4/index', {
        method: 'GET',
        headers: {
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
          'Accept-Language': 'zh-CN,zh;q=0.9',
          'Cache-Control': 'max-age=0',
          'Priority': 'u=0, i',
          'Sec-Ch-Ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
          'Sec-Ch-Ua-Mobile': '?0',
          'Sec-Ch-Ua-Platform': '"Windows"',
          'Sec-Fetch-Dest': 'empty',
          'Sec-Fetch-Mode': 'navigate',
          'Sec-Fetch-Site': 'cross-site',
          'Upgrade-Insecure-Requests': '1',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
          'Cookie': `sessionid=${sessionid}`
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const contentType = response.headers.get('content-type') || '';
      const responseText = await response.text();

      if (contentType.includes('application/json')) {
        // JSON响应
        const data = JSON.parse(responseText);
        return {
          content_type: contentType,
          user_info: data.user ? {
            id: data.user.id?.toString(),
            screen_name: data.user.screen_name,
            has_already_authentication: data.user.has_already_authentication
          } : undefined
        };
      } else {
        // HTML响应，需要解析
        const userInfo = extractUserInfoFromHTML(responseText);
        return {
          html_content: responseText,
          content_type: contentType,
          user_info: userInfo
        };
      }

    } catch (error) {
      lastError = error as Error;

      // 如果不是最后一次尝试，等待后重试
      if (attempt < maxRetries) {
        await new Promise(resolve => setTimeout(resolve, attempt * 2000));
      }
    }
  }

  throw lastError || new Error('获取账号详细信息失败');
}

/**
 * 从HTML内容中提取用户信息
 */
function extractUserInfoFromHTML(htmlContent: string): { id?: string; screen_name?: string; has_already_authentication?: boolean } {
  const userInfo: { id?: string; screen_name?: string; has_already_authentication?: boolean } = {};

  try {
    // 找到"user"的位置
    const userPos = htmlContent.indexOf('"user"');
    if (userPos === -1) {
      return userInfo;
    }

    // 从整个HTML开始搜索has_already_authentication（因为它在user之前）
    const authMatch = htmlContent.match(/"has_already_authentication":\s*(true|false)/);
    if (authMatch) {
      userInfo.has_already_authentication = authMatch[1] === 'true';
    }

    // 从"user"位置开始搜索，找到第一个"id"
    const searchContent = htmlContent.substring(userPos);

    // 提取ID
    const idMatch = searchContent.match(/"id":\s*(\d+)/);
    if (idMatch) {
      userInfo.id = idMatch[1];
    }

    // 提取screen_name
    const nameMatch = searchContent.match(/"screen_name":\s*"([^"]*)"/);
    if (nameMatch) {
      userInfo.screen_name = nameMatch[1];
    }

  } catch (error) {
    console.error('解析用户信息时出错:', error);
  }

  return userInfo;
}

/**
 * 获取账号禁言状态（带重试机制）
 */
async function fetchPunishStatus(uid: string, sessionid: string): Promise<ToutiaoPunishStatusResponse> {
  const maxRetries = 3;
  let lastError: Error | null = null;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const response = await fetch(`https://i.snssdk.com/author/appeal/punish/info/v1/?punish_type=2&app_id=0&crypto_uid=&validate_ticket=&uid=${uid}&punish_type_str=&aid=1231`, {
        method: 'GET',
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36',
          'Accept': 'application/json, text/plain, */*',
          'Accept-Language': 'zh-CN,zh;q=0.9',
          'Priority': 'u=1, i',
          'Referer': `https://i.snssdk.com/feoffline/toutiao_appeal/v1/tpl/mute-detail.html?uid=${uid}&punish_type=2`,
          'Sec-Ch-Ua': '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
          'Sec-Ch-Ua-Mobile': '?0',
          'Sec-Ch-Ua-Platform': '"Windows"',
          'Sec-Fetch-Dest': 'empty',
          'Sec-Fetch-Mode': 'cors',
          'Sec-Fetch-Site': 'same-origin',
          'Cookie': `sessionid=${sessionid}`
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json() as ToutiaoPunishStatusResponse;

    } catch (error) {
      lastError = error as Error;

      // 如果不是最后一次尝试，等待后重试
      if (attempt < maxRetries) {
        await new Promise(resolve => setTimeout(resolve, attempt * 2000));
      }
    }
  }

  throw lastError || new Error('获取禁言状态失败');
}

/**
 * 解析禁言状态响应，返回账号状态
 */
function parsePunishStatus(response: ToutiaoPunishStatusResponse): string {
  // 情况1: err_no = 1 且 err_tips = "禁言已解除"
  if (response.err_no === 1 && response.err_tips === "禁言已解除") {
    return '正常';
  }

  // 情况2: err_no = 0 且有 data.punish_time
  if (response.err_no === 0 && response.data && response.data.punish_time) {
    return '禁言';
  }

  // 情况3: err_no = 0 但没有 punish_time，说明正常
  if (response.err_no === 0) {
    return '正常';
  }

  // 其他情况，返回正常状态（默认）
  return '正常';
}

/**
 * 检查是否需要获取账号详细信息（用于新账号添加）
 */
function shouldFetchAccountInfoForNewAccount(username?: string, homepageUrl?: string, isVerified?: string): boolean {
  // 如果用户名为空、为"-"或者看起来不真实，则需要获取
  const needsUsername = !username || username === '-' || username.trim() === '';

  // 如果主页URL为空、为"-"或者不是有效的头条URL，则需要获取
  const needsHomepageUrl = !homepageUrl ||
                          homepageUrl === '-' ||
                          homepageUrl.trim() === '' ||
                          !homepageUrl.includes('toutiao.com');

  // 如果实名认证状态为空或未设置，则需要获取
  const needsVerificationStatus = !isVerified || isVerified === '-' || isVerified.trim() === '';

  return needsUsername || needsHomepageUrl || needsVerificationStatus;
}

/**
 * 检查是否需要获取账号详细信息（用于日常更新）
 */
function shouldFetchAccountInfoForDailyUpdate(isVerified?: string): boolean {
  // 只有实名认证为"否"的账号才需要每天更新账号详细信息
  return isVerified === '否';
}

/**
 * 为新添加的平台账号获取完整数据（包括账号详细信息）
 */
export async function fetchDataForNewAccount(phone: string, sessionid: string, currentUsername?: string, currentHomepageUrl?: string, currentIsVerified?: string): Promise<FetchResult> {
  const maxRetries = 3;
  let lastError: string = '';

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`  开始获取新账号 ${phone} 的完整数据... (尝试 ${attempt}/${maxRetries})`);
      console.log(`  sessionid前8位: ${sessionid.substring(0, 8)}...`);
      console.log(`  当前用户名: ${currentUsername || '未设置'}`);
      console.log(`  当前主页URL: ${currentHomepageUrl || '未设置'}`);
      console.log(`  当前实名状态: ${currentIsVerified || '未设置'}`);

      const result = await fetchDataForNewAccountSingle(phone, sessionid, currentUsername, currentHomepageUrl, currentIsVerified);

      // 如果成功获取到数据，直接返回
      if (result.success) {
        if (attempt > 1) {
          console.log(`  ✅ 第 ${attempt} 次尝试成功获取数据`);
        }
        return result;
      }

      // 如果失败，记录错误信息
      lastError = result.message;
      console.log(`  ❌ 第 ${attempt} 次尝试失败: ${result.message}`);

      // 如果不是最后一次尝试，等待后重试
      if (attempt < maxRetries) {
        const delay = attempt * 3000; // 3秒、6秒、9秒
        console.log(`  ⏳ 等待 ${delay/1000} 秒后重试...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }

    } catch (error) {
      lastError = `数据获取异常: ${error}`;
      console.log(`  ❌ 第 ${attempt} 次尝试异常: ${lastError}`);

      if (attempt < maxRetries) {
        const delay = attempt * 3000;
        console.log(`  ⏳ 等待 ${delay/1000} 秒后重试...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  // 所有重试都失败了
  console.log(`  ❌ 所有 ${maxRetries} 次尝试都失败，放弃获取数据`);
  return {
    success: false,
    phone,
    message: `经过 ${maxRetries} 次重试仍然失败: ${lastError}`,
    data: {}
  };
}

/**
 * 单次获取新账号数据（内部函数）
 */
async function fetchDataForNewAccountSingle(phone: string, sessionid: string, currentUsername?: string, currentHomepageUrl?: string, currentIsVerified?: string): Promise<FetchResult> {
  const result: FetchResult = {
    success: false,
    phone,
    message: '',
    data: {}
  };

  let hasData = false;
  let errors: string[] = [];

    // 检查是否需要获取账号详细信息
    const needsAccountInfo = shouldFetchAccountInfoForNewAccount(currentUsername, currentHomepageUrl, currentIsVerified);
    console.log(`  是否需要获取账号详细信息: ${needsAccountInfo}`);

    // 准备并发请求
    const promises: Promise<any>[] = [
      fetchIncomeData(sessionid),
      fetchBenefitData(sessionid)
    ];

    // 如果需要账号详细信息，添加到并发请求中
    if (needsAccountInfo) {
      promises.push(fetchAccountInfo(sessionid));
    }

    // 并发获取所有数据
    const results = await Promise.allSettled(promises);
    const [incomeResult, benefitResult, accountInfoResult] = results;

    // 处理收益数据
    if (incomeResult.status === 'fulfilled') {
      const incomeResponse = incomeResult.value;

      if (incomeResponse.code === 0 && incomeResponse.data && Array.isArray(incomeResponse.data)) {
        console.log(`  收益数据解析开始，数据项数量: ${incomeResponse.data.length}`);
        let totalIncome = 0;
        let yesterdayIncome = 0;
        let canWithdrawAmount = 0;
        let isYesterdayIncomeReady = false;

        for (const item of incomeResponse.data) {
          switch (item.type) {
            case 'period_income':
              yesterdayIncome = item.lastday || 0;
              isYesterdayIncomeReady = item.is_yesterday_income_ready || false;
              break;
            case 'total_income':
              totalIncome = item.total || 0;
              break;
            case 'can_withdraw_amount':
              canWithdrawAmount = item.total || 0;
              break;
          }
        }

        result.data!.total_income = String(totalIncome);
        result.data!.yesterday_income = String(yesterdayIncome);
        result.data!.can_withdraw_amount = String(canWithdrawAmount);
        result.isYesterdayIncomeReady = isYesterdayIncomeReady;
        hasData = true;
        console.log(`  收益数据解析成功`);
      } else if (incomeResponse.code === 100004) {
        console.log(`  账号 ${phone} 掉线: code=100004`);
        errors.push(`账号掉线: ${incomeResponse.message}`);
        result.accountOffline = true;
      } else {
        errors.push(`收益API返回错误: code=${incomeResponse.code}, message=${incomeResponse.message}`);
      }
    } else {
      errors.push(`收益获取失败: ${incomeResult.reason}`);
    }

    // 处理信用分和粉丝数数据
    if (benefitResult.status === 'fulfilled') {
      const benefitResponse = benefitResult.value;

      if (benefitResponse.code === 0 && benefitResponse.data) {
        const creditScore = benefitResponse.data.credit_info?.score || 0;
        const fansNum = benefitResponse.data.fans_num || 0;

        result.data!.credit_score = String(creditScore);
        result.data!.fans_num = String(fansNum);

        hasData = true;
        console.log(`  信用分和粉丝数解析成功: 信用分=${creditScore}, 粉丝数=${fansNum}`);
      } else if (benefitResponse.code === 100004 || benefitResponse.err_no === 100004) {
        if (!result.accountOffline) {
          errors.push(`账号掉线 (信用分API): ${benefitResponse.message}`);
          result.accountOffline = true;
        }
      } else {
        errors.push(`信用分API返回错误: code=${benefitResponse.code}, message=${benefitResponse.message}`);
      }
    } else {
      errors.push(`信用分和粉丝数获取失败: ${benefitResult.reason}`);
    }

    // 处理账号详细信息（如果需要的话）
    let userId: string | undefined;
    if (needsAccountInfo && accountInfoResult && accountInfoResult.status === 'fulfilled') {
      const accountInfoResponse = accountInfoResult.value;

      if (accountInfoResponse.user_info) {
        const userInfo = accountInfoResponse.user_info;
        userId = userInfo.id; // 保存用户ID用于后续获取禁言状态

        // 更新用户名
        if (userInfo.screen_name && (!currentUsername || currentUsername === '-' || currentUsername.trim() === '')) {
          result.data!.username = userInfo.screen_name;
          console.log(`  获取到用户名: ${userInfo.screen_name}`);
        }

        // 构建主页URL
        if (userInfo.id && (!currentHomepageUrl || currentHomepageUrl === '-' || currentHomepageUrl.trim() === '' || !currentHomepageUrl.includes('toutiao.com'))) {
          result.data!.homepage_url = `https://www.toutiao.com/c/user/${userInfo.id}/`;
          console.log(`  构建主页URL: ${result.data!.homepage_url}`);
        }

        // 更新实名认证状态
        if (userInfo.has_already_authentication !== undefined) {
          result.data!.is_verified = userInfo.has_already_authentication ? '是' : '否';
          console.log(`  实名认证状态: ${result.data!.is_verified}`);
        }

        hasData = true;
        console.log(`  账号详细信息获取成功，用户ID: ${userId}`);
      } else {
        errors.push('账号详细信息解析失败');
      }
    } else if (needsAccountInfo && accountInfoResult && accountInfoResult.status === 'rejected') {
      errors.push(`账号详细信息获取失败: ${accountInfoResult.reason}`);
    }

    // 获取禁言状态（如果有用户ID的话）
    if (userId) {
      try {
        console.log(`  开始获取用户 ${userId} 的禁言状态...`);
        const punishResponse = await fetchPunishStatus(userId, sessionid);
        const accountStatus = parsePunishStatus(punishResponse);

        result.data!.account_status = accountStatus;
        console.log(`  禁言状态获取成功: ${accountStatus}`);
        hasData = true;
      } catch (punishError) {
        console.log(`  禁言状态获取失败:`, punishError);
        errors.push(`禁言状态获取失败: ${punishError}`);
        // 设置默认状态
        result.data!.account_status = '正常';
      }
    } else {
      // 如果没有用户ID，设置默认状态
      result.data!.account_status = '正常';
      console.log(`  未获取到用户ID，设置默认状态为正常`);
    }

  if (hasData) {
    result.success = true;
    result.message = errors.length > 0 ? `部分成功: ${errors.join('; ')}` : '数据获取成功';
  } else {
    result.success = false;
    result.message = errors.join('; ');
  }

  return result;
}

/**
 * 为单个平台账号获取数据（用于日常收益更新）
 */
export async function fetchDataForAccount(phone: string, sessionid: string, currentIsVerified?: string): Promise<FetchResult> {
  const maxRetries = 2; // 日常更新重试次数少一些
  let lastError: string = '';

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`  开始获取账号 ${phone} 的数据... (尝试 ${attempt}/${maxRetries})`);
      console.log(`  sessionid前8位: ${sessionid.substring(0, 8)}...`);

      const result = await fetchDataForAccountSingle(phone, sessionid, currentIsVerified);

      // 如果成功获取到数据，直接返回
      if (result.success) {
        if (attempt > 1) {
          console.log(`  ✅ 第 ${attempt} 次尝试成功获取数据`);
        }
        return result;
      }

      // 如果失败，记录错误信息
      lastError = result.message;
      console.log(`  ❌ 第 ${attempt} 次尝试失败: ${result.message}`);

      // 如果不是最后一次尝试，等待后重试
      if (attempt < maxRetries) {
        const delay = attempt * 2000; // 2秒、4秒
        console.log(`  ⏳ 等待 ${delay/1000} 秒后重试...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }

    } catch (error) {
      lastError = `数据获取异常: ${error}`;
      console.log(`  ❌ 第 ${attempt} 次尝试异常: ${lastError}`);

      if (attempt < maxRetries) {
        const delay = attempt * 2000;
        console.log(`  ⏳ 等待 ${delay/1000} 秒后重试...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  // 所有重试都失败了
  console.log(`  ❌ 所有 ${maxRetries} 次尝试都失败，放弃获取数据`);
  return {
    success: false,
    phone,
    message: `经过 ${maxRetries} 次重试仍然失败: ${lastError}`,
    data: {}
  };
}

/**
 * 单次获取账号数据（内部函数）
 */
async function fetchDataForAccountSingle(phone: string, sessionid: string, currentIsVerified?: string): Promise<FetchResult> {
  const result: FetchResult = {
    success: false,
    phone,
    message: '',
    data: {}
  };

  let hasData = false;
  let errors: string[] = [];

    // 检查是否需要获取账号详细信息（只有实名认证为"否"的账号才需要）
    const needsAccountInfo = shouldFetchAccountInfoForDailyUpdate(currentIsVerified);
    console.log(`  是否需要获取账号详细信息: ${needsAccountInfo} (实名状态: ${currentIsVerified || '未知'})`);

    // 准备并发请求
    const promises: Promise<any>[] = [
      fetchIncomeData(sessionid),
      fetchBenefitData(sessionid)
    ];

    // 如果需要账号详细信息，添加到并发请求中
    if (needsAccountInfo) {
      promises.push(fetchAccountInfo(sessionid));
    }

    // 并发获取数据
    const results = await Promise.allSettled(promises);
    const [incomeResult, benefitResult, accountInfoResult] = results;

    // 处理收益数据
    if (incomeResult.status === 'fulfilled') {
      const incomeResponse = incomeResult.value;

      if (incomeResponse.code === 0 && incomeResponse.data && Array.isArray(incomeResponse.data)) {
        console.log(`  收益数据解析开始，数据项数量: ${incomeResponse.data.length}`);
        // 解析收益数据
        let totalIncome = 0;
        let yesterdayIncome = 0;
        let canWithdrawAmount = 0;
        let isYesterdayIncomeReady = false;

        for (const item of incomeResponse.data) {
          console.log(`  处理数据项:`, item);
          switch (item.type) {
            case 'period_income': // 昨日收益
              yesterdayIncome = item.lastday || 0;
              isYesterdayIncomeReady = item.is_yesterday_income_ready || false;
              console.log(`  昨日收益: ${yesterdayIncome}, 是否准备好: ${isYesterdayIncomeReady}`);
              break;
            case 'total_income': // 累计收益
              totalIncome = item.total || 0;
              console.log(`  累计收益: ${totalIncome}`);
              break;
            case 'can_withdraw_amount': // 可提现金额
              canWithdrawAmount = item.total || 0;
              console.log(`  可提现金额: ${canWithdrawAmount}`);
              break;
          }
        }

        result.data!.total_income = String(totalIncome);
        result.data!.yesterday_income = String(yesterdayIncome);
        result.data!.can_withdraw_amount = String(canWithdrawAmount);
        result.isYesterdayIncomeReady = isYesterdayIncomeReady;
        hasData = true;
        console.log(`  收益数据解析结果:`, {
          total_income: result.data!.total_income,
          yesterday_income: result.data!.yesterday_income,
          can_withdraw_amount: result.data!.can_withdraw_amount,
          isYesterdayIncomeReady: result.isYesterdayIncomeReady
        });
      } else if (incomeResponse.code === 100004) {
        // 用户未登录，sessionid过期
        console.log(`  账号 ${phone} 掉线: code=100004, message=${incomeResponse.message}`);
        errors.push(`账号掉线: ${incomeResponse.message}`);
        result.accountOffline = true; // 标记账号掉线
      } else {
        console.log(`  收益API返回错误: code=${incomeResponse.code}, message=${incomeResponse.message}`);
        errors.push(`收益API返回错误: code=${incomeResponse.code}, message=${incomeResponse.message}`);
      }
    } else {
      console.log(`  收益数据获取异常:`, incomeResult.reason);
      errors.push(`收益获取失败: ${incomeResult.reason}`);
    }

    // 处理信用分和粉丝数数据
    if (benefitResult.status === 'fulfilled') {
      const benefitResponse = benefitResult.value;

      if (benefitResponse.code === 0 && benefitResponse.data) {
        console.log(`  信用分和粉丝数数据解析开始`);

        // 获取信用分
        const creditScore = benefitResponse.data.credit_info?.score || 0;
        result.data!.credit_score = String(creditScore);
        console.log(`  信用分: ${creditScore}`);

        // 获取粉丝数
        const fansNum = benefitResponse.data.fans_num || 0;
        result.data!.fans_num = String(fansNum);
        console.log(`  粉丝数: ${fansNum}`);

        hasData = true;
        console.log(`  信用分和粉丝数解析结果:`, {
          credit_score: result.data!.credit_score,
          fans_num: result.data!.fans_num
        });
      } else if (benefitResponse.code === 100004 || benefitResponse.err_no === 100004) {
        // 用户未登录，sessionid过期
        console.log(`  账号 ${phone} 掉线 (信用分API): code=${benefitResponse.code}, err_no=${benefitResponse.err_no}, message=${benefitResponse.message}`);
        if (!result.accountOffline) { // 如果收益API没有标记掉线，这里标记
          errors.push(`账号掉线 (信用分API): ${benefitResponse.message}`);
          result.accountOffline = true;
        }
      } else {
        console.log(`  信用分API返回错误: code=${benefitResponse.code}, err_no=${benefitResponse.err_no}, message=${benefitResponse.message}`);
        errors.push(`信用分API返回错误: code=${benefitResponse.code}, message=${benefitResponse.message}`);
      }
    } else {
      console.log(`  信用分和粉丝数数据获取异常:`, benefitResult.reason);
      errors.push(`信用分和粉丝数获取失败: ${benefitResult.reason}`);
    }

    // 处理账号详细信息（如果需要的话）
    let userId: string | undefined;
    if (needsAccountInfo && accountInfoResult && accountInfoResult.status === 'fulfilled') {
      const accountInfoResponse = accountInfoResult.value;

      if (accountInfoResponse.user_info) {
        const userInfo = accountInfoResponse.user_info;
        userId = userInfo.id; // 保存用户ID用于后续获取禁言状态

        // 更新用户名（如果需要）
        if (userInfo.screen_name) {
          result.data!.username = userInfo.screen_name;
          console.log(`  获取到用户名: ${userInfo.screen_name}`);
        }

        // 构建主页URL（如果需要）
        if (userInfo.id) {
          result.data!.homepage_url = `https://www.toutiao.com/c/user/${userInfo.id}/`;
          console.log(`  构建主页URL: ${result.data!.homepage_url}`);
        }

        // 更新实名认证状态
        if (userInfo.has_already_authentication !== undefined) {
          result.data!.is_verified = userInfo.has_already_authentication ? '是' : '否';
          console.log(`  实名认证状态: ${result.data!.is_verified}`);
        }

        hasData = true;
        console.log(`  账号详细信息获取成功，用户ID: ${userId}`);
      } else {
        errors.push('账号详细信息解析失败');
      }
    } else if (needsAccountInfo && accountInfoResult && accountInfoResult.status === 'rejected') {
      errors.push(`账号详细信息获取失败: ${accountInfoResult.reason}`);
    }

    // 获取禁言状态（如果有用户ID的话）
    if (userId) {
      try {
        console.log(`  开始获取用户 ${userId} 的禁言状态...`);
        const punishResponse = await fetchPunishStatus(userId, sessionid);
        const accountStatus = parsePunishStatus(punishResponse);

        result.data!.account_status = accountStatus;
        console.log(`  禁言状态获取成功: ${accountStatus}`);
        hasData = true;
      } catch (punishError) {
        console.log(`  禁言状态获取失败:`, punishError);
        errors.push(`禁言状态获取失败: ${punishError}`);
        // 设置默认状态
        result.data!.account_status = '正常';
      }
    }

  if (hasData) {
    result.success = true;
    result.message = errors.length > 0 ? `部分成功: ${errors.join('; ')}` : '数据获取成功';
  } else {
    result.success = false;
    result.message = errors.join('; ');
  }

  return result;
}

/**
 * 检查账号是否需要更新数据
 * 如果昨日收益已准备好且是同一天，则跳过更新
 */
function shouldUpdateAccount(accountData: PlatformAccountData): boolean {
  const today = new Date().toISOString().split('T')[0]; // 获取今天的日期 YYYY-MM-DD
  const lastUpdateDate = accountData.data_update_time ? accountData.data_update_time.split('T')[0] : '';

  // 如果昨日收益已准备好且今天已经更新过，则跳过
  if ((accountData as any).is_yesterday_income_ready === true && lastUpdateDate === today) {
    return false;
  }

  return true;
}

/**
 * 仅获取收益数据（用于专门的收益更新）
 */
async function fetchIncomeDataOnly(phone: string, sessionid: string): Promise<FetchResult> {
  const result: FetchResult = {
    success: false,
    phone,
    message: '',
    data: {}
  };

  try {
    console.log(`  开始获取账号 ${phone} 的收益数据...`);
    console.log(`  sessionid前8位: ${sessionid.substring(0, 8)}...`);

    // 仅获取收益数据
    const incomeResponse = await fetchIncomeData(sessionid);

    if (incomeResponse.code === 0 && incomeResponse.data && Array.isArray(incomeResponse.data)) {
      console.log(`  收益数据解析开始，数据项数量: ${incomeResponse.data.length}`);
      // 解析收益数据
      let totalIncome = 0;
      let yesterdayIncome = 0;
      let canWithdrawAmount = 0;
      let isYesterdayIncomeReady = false;

      for (const item of incomeResponse.data) {
        switch (item.type) {
          case 'period_income': // 昨日收益
            yesterdayIncome = item.lastday || 0;
            isYesterdayIncomeReady = item.is_yesterday_income_ready || false;
            break;
          case 'total_income': // 累计收益
            totalIncome = item.total || 0;
            break;
          case 'can_withdraw_amount': // 可提现金额
            canWithdrawAmount = item.total || 0;
            break;
        }
      }

      // 使用 stats 对象结构，保持数据一致性 - 只更新收益相关字段
      if (!result.data!.stats) {
        result.data!.stats = {};
      }
      result.data!.stats.total_income = String(totalIncome);
      result.data!.stats.yesterday_income = String(yesterdayIncome);
      result.data!.stats.can_withdraw_amount = String(canWithdrawAmount);
      result.isYesterdayIncomeReady = isYesterdayIncomeReady;

      result.success = true;
      result.message = '收益数据获取成功';
      console.log(`  ✅ 收益数据获取成功: 总收益=${totalIncome}, 昨日收益=${yesterdayIncome}, 可提现=${canWithdrawAmount}`);
    } else {
      result.message = `收益数据获取失败: code=${incomeResponse.code}, message=${incomeResponse.message}`;
      console.log(`  ❌ 收益数据获取失败: code=${incomeResponse.code}, message=${incomeResponse.message}`);
    }

  } catch (error) {
    result.message = `获取收益数据时发生异常: ${error}`;
    console.error(`  ❌ 获取收益数据时发生异常:`, error);
  }

  return result;
}

/**
 * 仅获取信用分和粉丝数（用于专门的信用分和粉丝数更新）
 */
async function fetchCreditAndFansOnly(phone: string, sessionid: string): Promise<FetchResult> {
  const result: FetchResult = {
    success: false,
    phone,
    message: '',
    data: {}
  };

  try {
    console.log(`  开始获取账号 ${phone} 的信用分和粉丝数...`);
    console.log(`  sessionid前8位: ${sessionid.substring(0, 8)}...`);

    // 仅获取信用分和粉丝数
    const benefitResponse = await fetchBenefitData(sessionid);

    if (benefitResponse.code === 0 && benefitResponse.data) {
      console.log(`  信用分和粉丝数数据解析开始`);

      // 获取信用分
      const creditScore = benefitResponse.data.credit_info?.score || 0;
      const fansNum = benefitResponse.data.fans_num || 0;

      // 使用 stats 对象结构，保持数据一致性 - 只更新信用分和粉丝数字段
      if (!result.data!.stats) {
        result.data!.stats = {};
      }
      result.data!.stats.credit_score = String(creditScore);
      result.data!.stats.followers = String(fansNum);  // 统一使用 followers 字段

      console.log(`  信用分: ${creditScore}`);
      console.log(`  粉丝数: ${fansNum}`);

      result.success = true;
      result.message = '信用分和粉丝数获取成功';
      console.log(`  ✅ 信用分和粉丝数获取成功: 信用分=${creditScore}, 粉丝数=${fansNum}`);
    } else if (benefitResponse.code === 100004) {
      // 用户未登录，sessionid过期
      console.log(`  账号 ${phone} 掉线: code=100004, message=${benefitResponse.message}`);
      result.message = `账号掉线: ${benefitResponse.message}`;
      result.accountOffline = true;
    } else {
      result.message = `信用分和粉丝数获取失败: code=${benefitResponse.code}, message=${benefitResponse.message}`;
      console.log(`  ❌ 信用分和粉丝数获取失败: code=${benefitResponse.code}, message=${benefitResponse.message}`);
    }

  } catch (error) {
    result.message = `获取信用分和粉丝数时发生异常: ${error}`;
    console.error(`  ❌ 获取信用分和粉丝数时发生异常:`, error);
  }

  return result;
}

/**
 * 仅获取账号详细信息（用于专门的账号信息更新）
 */
async function fetchAccountInfoOnly(phone: string, sessionid: string): Promise<FetchResult> {
  const result: FetchResult = {
    success: false,
    phone,
    message: '',
    data: {}
  };

  try {
    console.log(`  开始获取账号 ${phone} 的详细信息...`);
    console.log(`  sessionid前8位: ${sessionid.substring(0, 8)}...`);

    // 获取账号详细信息
    const accountInfoResponse = await fetchAccountInfo(sessionid);
    let userId: string | undefined;

    if (accountInfoResponse.user_info) {
      const userInfo = accountInfoResponse.user_info;
      userId = userInfo.id; // 保存用户ID用于后续获取禁言状态

      // 更新用户名
      if (userInfo.screen_name) {
        result.data!.username = userInfo.screen_name;
        console.log(`  获取到用户名: ${userInfo.screen_name}`);
      }

      // 构建主页URL
      if (userInfo.id) {
        result.data!.homepage_url = `https://www.toutiao.com/c/user/${userInfo.id}/`;
        console.log(`  构建主页URL: ${result.data!.homepage_url}`);
      }

      // 更新实名认证状态
      if (userInfo.has_already_authentication !== undefined) {
        result.data!.is_verified = userInfo.has_already_authentication ? '是' : '否';
        console.log(`  实名认证状态: ${result.data!.is_verified}`);
      }

      console.log(`  账号详细信息获取成功，用户ID: ${userId}`);
    } else {
      result.message = '账号详细信息解析失败';
      console.log(`  ❌ 账号详细信息解析失败`);
      return result;
    }

    // 获取禁言状态（如果有用户ID的话）
    if (userId) {
      try {
        console.log(`  开始获取用户 ${userId} 的禁言状态...`);
        const punishResponse = await fetchPunishStatus(userId, sessionid);
        const accountStatus = parsePunishStatus(punishResponse);

        result.data!.account_status = accountStatus;
        console.log(`  禁言状态获取成功: ${accountStatus}`);
      } catch (punishError) {
        console.log(`  禁言状态获取失败:`, punishError);
        result.data!.account_status = '未知';
      }
    }

    result.success = true;
    result.message = '账号详细信息获取成功';
    console.log(`  ✅ 账号详细信息获取成功: 用户名=${result.data!.username}, 实名状态=${result.data!.is_verified}, 账号状态=${result.data!.account_status}`);

  } catch (error) {
    result.message = `获取账号详细信息时发生异常: ${error}`;
    console.error(`  ❌ 获取账号详细信息时发生异常:`, error);
  }

  return result;
}

/**
 * 批量获取所有平台账号的数据并更新数据库
 * 智能更新：如果昨日收益已准备好，则跳过该账号的更新
 */
/**
 * 智能批量获取数据 - 分阶段获取策略
 * 阶段1: 10:00开始，每30分钟检查第一个账号，直到昨日收益准备好
 * 阶段2: 昨日收益准备好后，逐个获取，遇到未准备好的账号则等待10分钟
 */
export async function smartBatchFetchAndUpdateData(
  db: D1Database,
  ctx?: ExecutionContext
): Promise<{
  success: boolean;
  message: string;
  totalAccounts: number;
  successCount: number;
  failureCount: number;
  skippedCount: number;
  phase: 'waiting' | 'processing' | 'completed';
  results: FetchResult[];
}> {
  try {
    console.log('=== 开始智能批量获取平台账号数据 ===');
    const startTime = new Date();
    const beijingTime = new Date(startTime.getTime() + 8 * 60 * 60 * 1000);
    const beijingHour = beijingTime.getUTCHours();

    // 获取所有主账号的平台数据
    const allMainAccountsData = await db.prepare(`
      SELECT main_account_id, platform_accounts_data FROM main_account_platform_data
    `).all<{ main_account_id: number; platform_accounts_data: string }>();

    if (!allMainAccountsData.results || allMainAccountsData.results.length === 0) {
      return {
        success: true,
        message: "没有找到任何平台账号数据",
        totalAccounts: 0,
        successCount: 0,
        failureCount: 0,
        skippedCount: 0,
        phase: 'completed',
        results: []
      };
    }

    const results: FetchResult[] = [];
    let totalAccounts = 0;
    let successCount = 0;
    let failureCount = 0;
    let skippedCount = 0;

    // 收集所有需要处理的账号
    const allAccounts: Array<{
      mainAccountId: number;
      phone: string;
      accountData: PlatformAccountData;
      platformData: any;
    }> = [];

    for (const mainAccountData of allMainAccountsData.results) {
      try {
        const platformData = validatePlatformAccountsCollection(mainAccountData.platform_accounts_data);
        if (!platformData) continue;

        for (const [phone, accountData] of Object.entries(platformData.accounts)) {
          if (accountData.sessionid) {
            allAccounts.push({
              mainAccountId: mainAccountData.main_account_id,
              phone,
              accountData,
              platformData
            });
            totalAccounts++;
          }
        }
      } catch (error) {
        continue;
      }
    }

    if (allAccounts.length === 0) {
      return {
        success: true,
        message: "没有找到有效的平台账号",
        totalAccounts: 0,
        successCount: 0,
        failureCount: 0,
        skippedCount: 0,
        phase: 'completed',
        results: []
      };
    }

    // 检查是否需要进入等待阶段
    // 在10:00-11:00之间，每30分钟检查第一个账号
    const shouldCheckFirstAccount = beijingHour === 10 ||
      (beijingHour < 12 && beijingTime.getUTCMinutes() % 30 === 0);

    if (shouldCheckFirstAccount) {
      console.log('检查第一个账号的昨日收益准备状态...');
      const firstAccount = allAccounts[0];

      try {
        const incomeResponse = await fetchIncomeData(firstAccount.accountData.sessionid);

        // 检查昨日收益是否准备好
        let isYesterdayIncomeReady = false;
        if (incomeResponse.code === 0 && incomeResponse.data && Array.isArray(incomeResponse.data)) {
          for (const item of incomeResponse.data) {
            if (item.type === 'period_income' && item.is_yesterday_income_ready) {
              isYesterdayIncomeReady = true;
              break;
            }
          }
        }

        if (!isYesterdayIncomeReady) {
          console.log('昨日收益尚未准备好，30分钟后再次检查...');

          // 动态调度30分钟后的任务
          if (ctx) {
            const nextCheckTime = new Date(Date.now() + 30 * 60 * 1000);
            console.log(`调度下次检查任务: ${nextCheckTime.toISOString()}`);

            ctx.waitUntil(
              scheduleNextCheck(db, nextCheckTime, ctx)
            );
          }

          return {
            success: true,
            message: `昨日收益尚未准备好，已调度30分钟后再次检查 (北京时间 ${beijingHour}:${beijingTime.getUTCMinutes().toString().padStart(2, '0')})`,
            totalAccounts,
            successCount: 0,
            failureCount: 0,
            skippedCount: totalAccounts,
            phase: 'waiting',
            results: []
          };
        }

        console.log('昨日收益已准备好，开始处理所有账号...');
      } catch (error) {
        console.log(`检查第一个账号失败: ${error}，等待30分钟后再次检查...`);
        return {
          success: true,
          message: `检查第一个账号失败，等待中... (${error})`,
          totalAccounts,
          successCount: 0,
          failureCount: 0,
          skippedCount: totalAccounts,
          phase: 'waiting',
          results: []
        };
      }
    }

    // 阶段2: 处理阶段 - 逐个处理账号
    console.log('阶段2: 开始逐个处理账号...');

    for (const account of allAccounts) {
      // 检查账号是否需要更新
      const needsUpdate = shouldUpdateAccount(account.accountData);
      if (!needsUpdate) {
        console.log(`账号 ${account.phone} 昨日收益已准备好，跳过更新`);
        const skipResult: FetchResult = {
          success: true,
          phone: account.phone,
          message: '昨日收益已准备好，跳过更新',
          isYesterdayIncomeReady: true
        };
        results.push(skipResult);
        skippedCount++;
        continue;
      }

      // 获取数据
      console.log(`开始获取账号 ${account.phone} 的收益数据...`);
      const fetchResult = await fetchDataForAccount(account.phone, account.accountData.sessionid, account.accountData.is_verified);
      results.push(fetchResult);

      if (fetchResult.success && fetchResult.data) {
        // 合并更新数据库 - 保留现有字段，只更新传入的字段
        if (fetchResult.data.stats) {
          // 确保 stats 对象存在
          if (!account.accountData.stats) {
            account.accountData.stats = {};
          }

          // 只更新传入的字段，保留其他字段
          if (fetchResult.data.stats.total_income !== undefined) {
            account.accountData.stats.total_income = fetchResult.data.stats.total_income;
          }
          if (fetchResult.data.stats.yesterday_income !== undefined) {
            account.accountData.stats.yesterday_income = fetchResult.data.stats.yesterday_income;
          }
          if (fetchResult.data.stats.can_withdraw_amount !== undefined) {
            account.accountData.stats.can_withdraw_amount = fetchResult.data.stats.can_withdraw_amount;
          }
          if (fetchResult.data.stats.credit_score !== undefined) {
            account.accountData.stats.credit_score = fetchResult.data.stats.credit_score;
          }
          if (fetchResult.data.stats.followers !== undefined) {
            account.accountData.stats.followers = fetchResult.data.stats.followers;
          }
        }

        // 更新其他基本信息字段
        if (fetchResult.data.username !== undefined) {
          account.accountData.username = fetchResult.data.username;
        }
        if (fetchResult.data.homepage_url !== undefined) {
          account.accountData.homepage_url = fetchResult.data.homepage_url;
        }
        if (fetchResult.data.account_status !== undefined) {
          account.accountData.account_status = fetchResult.data.account_status;
        }
        if (fetchResult.data.is_verified !== undefined) {
          account.accountData.is_verified = fetchResult.data.is_verified;
        }

        account.accountData.data_update_time = new Date().toISOString();
        account.accountData.account_status = '正常';
        successCount++;

        // 保存到数据库
        account.platformData.metadata.last_batch_update = new Date().toISOString();
        await db.prepare(`
          UPDATE main_account_platform_data
          SET platform_accounts_data = ?, updated_at = ?
          WHERE main_account_id = ?
        `).bind(JSON.stringify(account.platformData), new Date().toISOString(), account.mainAccountId).run();

      } else if (fetchResult.accountOffline) {
        account.accountData.account_status = '掉线';
        account.accountData.data_update_time = new Date().toISOString();
        failureCount++;
      } else {
        failureCount++;
      }

      // 如果遇到昨日收益未准备好的账号，停止处理后续账号，等待10分钟
      if (fetchResult.success && !fetchResult.isYesterdayIncomeReady) {
        console.log(`账号 ${account.phone} 昨日收益未准备好，停止处理后续账号，等待10分钟后继续`);

        // 计算剩余未处理的账号数
        const remainingAccounts = allAccounts.length - (successCount + failureCount + skippedCount);

        // 动态调度10分钟后的任务
        if (ctx) {
          const nextProcessTime = new Date(Date.now() + 10 * 60 * 1000);
          console.log(`调度10分钟后继续处理任务: ${nextProcessTime.toISOString()}`);

          ctx.waitUntil(
            scheduleNextProcess(db, nextProcessTime, ctx)
          );
        }

        return {
          success: true,
          message: `处理到账号 ${account.phone} 时昨日收益未准备好，已调度10分钟后继续处理剩余 ${remainingAccounts} 个账号`,
          totalAccounts,
          successCount,
          failureCount,
          skippedCount,
          phase: 'processing',
          results
        };
      }

      // 添加延迟避免请求过于频繁
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    const message = `智能数据获取完成: 总计${totalAccounts}个账号，成功${successCount}个，失败${failureCount}个，跳过${skippedCount}个`;
    const isCompleted = successCount + failureCount + skippedCount >= totalAccounts;

    // 准备返回结果
    const fetchResult: any = {
      success: true,
      message,
      totalAccounts,
      successCount,
      failureCount,
      skippedCount,
      phase: isCompleted ? 'completed' : 'processing',
      results
    };

    // 如果数据获取完成，准备按主账号分组的数据用于推送
    if (isCompleted && ctx) {
      console.log('数据获取完成，准备按主账号分组推送数据...');

      // 按主账号分组整理数据
      const mainAccountGroups = await groupResultsByMainAccount(db, results);

      // 添加推送数据到结果中
      fetchResult.mainAccountGroups = mainAccountGroups;
      fetchResult.needsPush = true;

      console.log(`=== 数据获取完成，共 ${Object.keys(mainAccountGroups).length} 个主账号需要推送数据 ===`);
    }

    return fetchResult;

  } catch (error) {
    console.error('智能批量获取数据失败:', error);
    return {
      success: false,
      message: `智能批量获取数据失败: ${error}`,
      totalAccounts: 0,
      successCount: 0,
      failureCount: 0,
      skippedCount: 0,
      phase: 'waiting',
      results: []
    };
  }
}

export async function batchFetchAndUpdateData(db: D1Database): Promise<{
  success: boolean;
  message: string;
  totalAccounts: number;
  successCount: number;
  failureCount: number;
  skippedCount: number;
  results: FetchResult[];
}> {
  try {
    console.log('=== 开始批量获取平台账号数据 ===');
    const startTime = new Date();

    // 获取所有主账号的平台数据
    console.log('查询数据库中的主账号平台数据...');
    const allMainAccountsData = await db.prepare(`
      SELECT main_account_id, platform_accounts_data FROM main_account_platform_data
    `).all<{ main_account_id: number; platform_accounts_data: string }>();

    console.log(`数据库查询完成，找到 ${allMainAccountsData.results?.length || 0} 个主账号`);

    // 列出所有主账号ID
    if (allMainAccountsData.results && allMainAccountsData.results.length > 0) {
      const mainAccountIds = allMainAccountsData.results.map(account => account.main_account_id);
      console.log(`主账号ID列表: [${mainAccountIds.join(', ')}]`);
    }

    if (!allMainAccountsData.results || allMainAccountsData.results.length === 0) {
      return {
        success: true,
        message: "没有找到任何平台账号数据",
        totalAccounts: 0,
        successCount: 0,
        failureCount: 0,
        skippedCount: 0,
        results: []
      };
    }

    const results: FetchResult[] = [];
    let totalAccounts = 0;
    let successCount = 0;
    let failureCount = 0;
    let skippedCount = 0;

    // 遍历所有主账号
    console.log(`开始遍历 ${allMainAccountsData.results.length} 个主账号...`);
    for (let i = 0; i < allMainAccountsData.results.length; i++) {
      const mainAccountData = allMainAccountsData.results[i];
      console.log(`\n=== 处理第 ${i + 1}/${allMainAccountsData.results.length} 个主账号 ===`);
      const mainAccountStartTime = new Date();
      try {
        console.log(`处理主账号 ID: ${mainAccountData.main_account_id}`);
        console.log(`原始数据长度: ${mainAccountData.platform_accounts_data.length} 字符`);
        console.log(`原始数据前200字符: ${mainAccountData.platform_accounts_data.substring(0, 200)}...`);

        // 使用验证函数解析JSON数据
        const platformData = validatePlatformAccountsCollection(mainAccountData.platform_accounts_data);
        if (!platformData) {
          console.error(`主账号 ${mainAccountData.main_account_id} 的平台数据格式无效，跳过`);
          console.error(`无效数据内容: ${mainAccountData.platform_accounts_data}`);
          continue;
        }

        const platformAccountsCount = Object.keys(platformData.accounts).length;
        console.log(`主账号 ${mainAccountData.main_account_id} 下有 ${platformAccountsCount} 个平台账号`);

        // 遍历该主账号下的所有平台账号
        for (const [phone, accountData] of Object.entries(platformData.accounts)) {
          totalAccounts++;
          console.log(`\n处理平台账号: ${phone}`);
          console.log(`账号状态: ${accountData.account_status || '未知'}`);
          console.log(`是否有sessionid: ${accountData.sessionid ? '是' : '否'}`);

          if (!accountData.sessionid) {
            console.log(`账号 ${phone} 缺少sessionid，跳过`);
            const failResult: FetchResult = {
              success: false,
              phone,
              message: '缺少sessionid'
            };
            results.push(failResult);
            failureCount++;
            continue;
          }

          // 检查是否需要更新
          const needsUpdate = shouldUpdateAccount(accountData);
          console.log(`账号 ${phone} 是否需要更新: ${needsUpdate}`);

          if (!needsUpdate) {
            console.log(`账号 ${phone} 昨日收益已准备好，跳过更新`);
            const skipResult: FetchResult = {
              success: true,
              phone,
              message: '昨日收益已准备好，跳过更新',
              isYesterdayIncomeReady: true
            };
            results.push(skipResult);
            skippedCount++;
            continue;
          }

          // 获取数据
          console.log(`开始获取账号 ${phone} 的收益数据...`);
          const fetchResult = await fetchDataForAccount(phone, accountData.sessionid, accountData.is_verified);
          console.log(`账号 ${phone} 数据获取结果:`, {
            success: fetchResult.success,
            message: fetchResult.message,
            hasData: !!fetchResult.data,
            isYesterdayIncomeReady: fetchResult.isYesterdayIncomeReady,
            accountOffline: fetchResult.accountOffline
          });
          results.push(fetchResult);

          if (fetchResult.success && fetchResult.data) {
            console.log(`账号 ${phone} 数据获取成功，开始更新数据库...`);
            console.log(`获取到的数据:`, fetchResult.data);

            // 更新收益相关数据
            if (fetchResult.data.total_income !== undefined) {
              console.log(`更新总收益: ${accountData.stats.total_income} -> ${fetchResult.data.total_income}`);
              accountData.stats.total_income = fetchResult.data.total_income;
            }
            if (fetchResult.data.yesterday_income !== undefined) {
              console.log(`更新昨日收益: ${accountData.stats.yesterday_income} -> ${fetchResult.data.yesterday_income}`);
              accountData.stats.yesterday_income = fetchResult.data.yesterday_income;
            }
            if (fetchResult.data.can_withdraw_amount !== undefined) {
              console.log(`更新可提现金额: ${accountData.stats.can_withdraw_amount || '未设置'} -> ${fetchResult.data.can_withdraw_amount}`);
              accountData.stats.can_withdraw_amount = fetchResult.data.can_withdraw_amount;
            }

            // 更新信用分和粉丝数
            if (fetchResult.data.credit_score !== undefined) {
              console.log(`更新信用分: ${accountData.stats.credit_score || '未设置'} -> ${fetchResult.data.credit_score}`);
              accountData.stats.credit_score = fetchResult.data.credit_score;
            }
            if (fetchResult.data.fans_num !== undefined) {
              console.log(`更新粉丝数: ${accountData.stats.followers || '未设置'} -> ${fetchResult.data.fans_num}`);
              accountData.stats.followers = fetchResult.data.fans_num; // 存储到followers字段
            }

            // 更新数据更新时间
            accountData.data_update_time = new Date().toISOString();
            console.log(`更新数据时间: ${accountData.data_update_time}`);

            // 添加昨日收益准备状态标记（存储在账号数据中）
            if (fetchResult.isYesterdayIncomeReady !== undefined) {
              console.log(`昨日收益准备状态: ${fetchResult.isYesterdayIncomeReady}`);
              (accountData as any).is_yesterday_income_ready = fetchResult.isYesterdayIncomeReady;
            }

            // 如果账号之前是掉线状态，现在获取成功了，恢复正常状态
            if (accountData.account_status === '掉线') {
              console.log(`账号 ${phone} 从掉线状态恢复为正常状态`);
              accountData.account_status = '正常';
            }

            successCount++;
            console.log(`账号 ${phone} 数据更新完成`);
          } else if (fetchResult.accountOffline) {
            console.log(`账号 ${phone} 掉线，更新状态`);
            // 账号掉线，更新账号状态
            accountData.account_status = '掉线';
            accountData.data_update_time = new Date().toISOString();
            failureCount++;
          } else {
            console.log(`账号 ${phone} 数据获取失败: ${fetchResult.message}`);
            failureCount++;
          }

          // 添加延迟避免请求过于频繁
          await new Promise(resolve => setTimeout(resolve, 1000));
        }

        // 更新该主账号的平台数据
        console.log(`更新主账号 ${mainAccountData.main_account_id} 的数据库记录...`);
        const updateResult = await db.prepare(`
          UPDATE main_account_platform_data
          SET platform_accounts_data = ?, updated_at = ?
          WHERE main_account_id = ?
        `).bind(
          JSON.stringify(platformData),
          new Date().toISOString(),
          mainAccountData.main_account_id
        ).run();

        console.log(`主账号 ${mainAccountData.main_account_id} 数据库更新结果:`, {
          success: updateResult.success,
          meta: updateResult.meta
        });

        const mainAccountEndTime = new Date();
        const mainAccountDuration = mainAccountEndTime.getTime() - mainAccountStartTime.getTime();
        const totalElapsed = mainAccountEndTime.getTime() - startTime.getTime();
        console.log(`=== 主账号 ${mainAccountData.main_account_id} 处理完成，耗时: ${mainAccountDuration}ms ===`);
        console.log(`总执行时间: ${totalElapsed}ms`);

        // 检查是否接近执行时间限制（Cloudflare Workers定时任务通常有30秒限制）
        if (totalElapsed > 25000) { // 25秒警告
          console.warn(`⚠️ 执行时间已超过25秒，可能接近定时任务时间限制！`);
        }

      } catch (error) {
        const mainAccountEndTime = new Date();
        const mainAccountDuration = mainAccountEndTime.getTime() - mainAccountStartTime.getTime();
        console.error(`处理主账号 ${mainAccountData.main_account_id} 的数据时出错:`, error);
        console.log(`=== 主账号 ${mainAccountData.main_account_id} 处理失败，耗时: ${mainAccountDuration}ms ===`);
      }
    }

    console.log(`\n=== 所有主账号处理完成 ===`);

    const endTime = new Date();
    const duration = endTime.getTime() - startTime.getTime();
    const message = `数据获取完成: 总计${totalAccounts}个账号，成功${successCount}个，失败${failureCount}个，跳过${skippedCount}个`;

    return {
      success: true,
      message,
      totalAccounts,
      successCount,
      failureCount,
      skippedCount,
      results
    };

  } catch (error) {
    console.error('批量获取数据失败:', error);
    return {
      success: false,
      message: `批量获取数据失败: ${error}`,
      totalAccounts: 0,
      successCount: 0,
      failureCount: 0,
      skippedCount: 0,
      results: []
    };
  }
}

/**
 * 动态调度下次检查任务（30分钟后）
 */
async function scheduleNextCheck(db: D1Database, nextTime: Date, ctx: ExecutionContext): Promise<void> {
  try {
    console.log(`等待到 ${nextTime.toISOString()} 执行下次检查...`);

    const delay = nextTime.getTime() - Date.now();
    if (delay > 0) {
      await new Promise(resolve => setTimeout(resolve, delay));
    }

    console.log('执行调度的检查任务...');
    await smartBatchFetchAndUpdateData(db, ctx);
  } catch (error) {
    console.error('调度的检查任务执行失败:', error);
  }
}

/**
 * 动态调度下次处理任务（10分钟后）
 */
async function scheduleNextProcess(db: D1Database, nextTime: Date, ctx: ExecutionContext): Promise<void> {
  try {
    console.log(`等待到 ${nextTime.toISOString()} 执行下次处理...`);

    const delay = nextTime.getTime() - Date.now();
    if (delay > 0) {
      await new Promise(resolve => setTimeout(resolve, delay));
    }

    console.log('执行调度的处理任务...');
    await smartBatchFetchAndUpdateData(db, ctx);
  } catch (error) {
    console.error('调度的处理任务执行失败:', error);
  }
}

/**
 * 按主账号分组整理结果数据
 */
async function groupResultsByMainAccount(db: D1Database, results: FetchResult[]): Promise<Record<number, any>> {
  try {
    console.log('开始按主账号分组整理数据...');

    // 获取所有主账号的平台数据
    const allMainAccountsData = await db.prepare(`
      SELECT main_account_id, platform_accounts_data FROM main_account_platform_data
    `).all<{ main_account_id: number; platform_accounts_data: string }>();

    const mainAccountGroups: Record<number, any> = {};

    if (!allMainAccountsData.results) {
      return mainAccountGroups;
    }

    // 遍历每个主账号
    for (const mainAccountData of allMainAccountsData.results) {
      try {
        const platformData = validatePlatformAccountsCollection(mainAccountData.platform_accounts_data);
        if (!platformData) continue;

        const mainAccountId = mainAccountData.main_account_id;
        const platformAccounts: any[] = [];

        // 收集该主账号下的所有平台账号数据
        for (const [phone, accountData] of Object.entries(platformData.accounts)) {
          // 查找该手机号的处理结果
          const result = results.find(r => r.phone === phone);

          platformAccounts.push({
            phone,
            platform: accountData.platform || '头条号',
            username: accountData.username || '',
            login_type: accountData.login_type || '',
            team_tag: accountData.team_tag || '',
            account_status: accountData.account_status || '',
            is_verified: accountData.is_verified || '否', // 实名认证状态
            stats: {
              total_income: accountData.stats?.total_income || '0',
              yesterday_income: accountData.stats?.yesterday_income || '0',
              can_withdraw_amount: accountData.stats?.can_withdraw_amount || '0',
              yesterday_reads: accountData.stats?.yesterday_reads || '0',
              total_reads: accountData.stats?.total_reads || '0',
              credit_score: accountData.stats?.credit_score || '0',
              followers: accountData.stats?.followers || '0'
            },
            data_update_time: accountData.data_update_time || '',
            updateResult: result ? {
              success: result.success,
              message: result.message,
              isYesterdayIncomeReady: result.isYesterdayIncomeReady,
              accountOffline: result.accountOffline
            } : null
          });
        }

        if (platformAccounts.length > 0) {
          mainAccountGroups[mainAccountId] = {
            mainAccountId,
            platformAccounts,
            metadata: {
              last_batch_update: platformData.metadata?.last_batch_update || '',
              total_accounts: platformAccounts.length,
              updated_accounts: platformAccounts.filter(acc => acc.updateResult?.success).length
            }
          };
        }

      } catch (error) {
        console.error(`处理主账号 ${mainAccountData.main_account_id} 数据时出错:`, error);
        continue;
      }
    }

    console.log(`按主账号分组完成，共 ${Object.keys(mainAccountGroups).length} 个主账号`);
    return mainAccountGroups;

  } catch (error) {
    console.error('按主账号分组数据失败:', error);
    return {};
  }
}

/**
 * 推送通知给管理员
 */
async function notifyAdmins(stats: { totalAccounts: number; successCount: number; failureCount: number; skippedCount: number }): Promise<void> {
  try {
    const message = `数据获取完成: 总计${stats.totalAccounts}个账号，成功${stats.successCount}个，失败${stats.failureCount}个，跳过${stats.skippedCount}个`;

    // 调用UserAuthDO的通知接口
    const notifyResponse = await fetch('https://your-worker-domain.com/notify-admin', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        type: 'data_update_completed',
        message: message,
        data: {
          totalAccounts: stats.totalAccounts,
          successCount: stats.successCount,
          failureCount: stats.failureCount,
          skippedCount: stats.skippedCount,
          timestamp: new Date().toISOString()
        }
      })
    });

    if (notifyResponse.ok) {
      console.log('成功推送数据更新完成通知给管理员');
    } else {
      console.error('推送管理员通知失败:', await notifyResponse.text());
    }
  } catch (error) {
    console.error('推送管理员通知异常:', error);
  }
}

/**
 * 推送通知给有数据更新的用户
 */
async function notifyAffectedUsers(results: FetchResult[]): Promise<void> {
  try {
    // 收集有数据更新的用户
    const successfulUpdates = results.filter(result => result.success && result.data);

    console.log(`准备推送通知给 ${successfulUpdates.length} 个用户`);

    for (const result of successfulUpdates) {
      try {
        // 根据手机号查找用户ID（需要查询数据库）
        // 这里简化处理，实际应该查询数据库获取用户ID
        const message = `您的收益数据已更新`;
        const data = {
          phone: result.phone,
          totalIncome: result.data?.total_income,
          yesterdayIncome: result.data?.yesterday_income,
          canWithdrawAmount: result.data?.can_withdraw_amount,
          timestamp: new Date().toISOString()
        };

        // 注意：这里需要实际的用户ID，暂时跳过用户通知
        // 因为我们需要从手机号查找用户ID，这需要数据库查询
        console.log(`用户 ${result.phone} 的数据已更新:`, data);

      } catch (error) {
        console.error(`推送用户 ${result.phone} 通知失败:`, error);
      }
    }
  } catch (error) {
    console.error('推送用户通知异常:', error);
  }
}

/**
 * 批量获取并更新所有平台账号的全部信息（强制更新模式）
 * 包括：收益数据、信用分、粉丝数、禁言状态、实名状态
 */
export async function batchFetchAndUpdateAllData(db: D1Database): Promise<{
  success: boolean;
  message: string;
  totalAccounts: number;
  successCount: number;
  failureCount: number;
  skippedCount: number;
  results: FetchResult[];
}> {
  const startTime = new Date();
  console.log(`\n=== 开始批量更新全部信息 (${startTime.toISOString()}) ===`);

  let totalAccounts = 0;
  let successCount = 0;
  let failureCount = 0;
  let skippedCount = 0;
  const results: FetchResult[] = [];

  try {
    // 获取所有主账号的平台数据
    const allMainAccountsData = await db.prepare(`
      SELECT main_account_id, platform_accounts_data FROM main_account_platform_data
    `).all<{ main_account_id: number; platform_accounts_data: string }>();

    if (!allMainAccountsData.results || allMainAccountsData.results.length === 0) {
      console.log('没有找到任何主账号数据');
      return {
        success: true,
        message: '没有找到任何主账号数据',
        totalAccounts: 0,
        successCount: 0,
        failureCount: 0,
        skippedCount: 0,
        results: []
      };
    }

    console.log(`找到 ${allMainAccountsData.results.length} 个主账号`);

    // 收集所有需要更新的账号
    const allAccounts: Array<{
      mainAccountId: number;
      phone: string;
      accountData: PlatformAccountData;
    }> = [];

    for (const mainAccountData of allMainAccountsData.results) {
      try {
        const platformData = validatePlatformAccountsCollection(mainAccountData.platform_accounts_data);
        if (!platformData) {
          console.log(`主账号 ${mainAccountData.main_account_id} 的平台数据格式无效，跳过`);
          continue;
        }

        // 添加该主账号下的所有平台账号
        for (const [phone, accountData] of Object.entries(platformData.accounts)) {
          allAccounts.push({
            mainAccountId: mainAccountData.main_account_id,
            phone,
            accountData
          });
        }
      } catch (error) {
        console.error(`处理主账号 ${mainAccountData.main_account_id} 数据时出错:`, error);
        continue;
      }
    }

    totalAccounts = allAccounts.length;
    console.log(`总共需要更新 ${totalAccounts} 个平台账号的全部信息`);

    if (totalAccounts === 0) {
      return {
        success: true,
        message: '没有找到需要更新的平台账号',
        totalAccounts: 0,
        successCount: 0,
        failureCount: 0,
        skippedCount: 0,
        results: []
      };
    }

    // 批量处理所有账号（强制获取全部信息）
    for (let i = 0; i < allAccounts.length; i++) {
      const account = allAccounts[i];
      console.log(`\n[${i + 1}/${totalAccounts}] 更新账号 ${account.phone} 的全部信息...`);

      try {
        // 强制获取全部信息（包括账号详细信息）
        const fetchResult = await fetchDataForNewAccount(
          account.phone,
          account.accountData.sessionid,
          account.accountData.username,
          account.accountData.homepage_url,
          account.accountData.is_verified
        );

        results.push(fetchResult);

        if (fetchResult.success) {
          console.log(`  ✅ 账号 ${account.phone} 数据获取成功`);

          // 更新数据库中的账号信息
          if (fetchResult.data) {
            try {
              const updateResult = await updateSinglePlatformAccount(
                db,
                account.mainAccountId,
                account.phone,
                fetchResult.data
              );

              if (updateResult.success) {
                successCount++;
                console.log(`  ✅ 账号 ${account.phone} 数据库更新成功`);
              } else {
                failureCount++;
                console.error(`  ❌ 账号 ${account.phone} 数据库保存失败: ${updateResult.message}`);

                // 更新结果记录
                results[results.length - 1] = {
                  success: false,
                  phone: account.phone,
                  message: `数据库保存失败: ${updateResult.message}`,
                  data: fetchResult.data
                };
              }
            } catch (updateError) {
              failureCount++;
              console.error(`  ❌ 账号 ${account.phone} 数据库保存异常:`, updateError);

              // 更新结果记录
              results[results.length - 1] = {
                success: false,
                phone: account.phone,
                message: `数据库保存异常: ${updateError}`,
                data: fetchResult.data
              };
            }
          } else {
            failureCount++;
            console.error(`  ❌ 账号 ${account.phone} 获取到的数据为空`);

            // 更新结果记录
            results[results.length - 1] = {
              success: false,
              phone: account.phone,
              message: '获取到的数据为空'
            };
          }
        } else {
          failureCount++;
          console.log(`  ❌ 账号 ${account.phone} 数据获取失败: ${fetchResult.message}`);
        }

        // 添加延迟避免请求过于频繁
        if (i < allAccounts.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }

      } catch (error) {
        failureCount++;
        const errorMessage = `更新异常: ${error}`;
        console.error(`  ❌ 账号 ${account.phone} 更新异常:`, error);

        results.push({
          success: false,
          phone: account.phone,
          message: errorMessage
        });
      }
    }

    console.log(`\n=== 全部信息更新完成 ===`);

    const endTime = new Date();
    const message = `全部信息更新完成: 总计${totalAccounts}个账号，成功${successCount}个，失败${failureCount}个，跳过${skippedCount}个`;

    return {
      success: true,
      message,
      totalAccounts,
      successCount,
      failureCount,
      skippedCount,
      results
    };

  } catch (error) {
    console.error('批量更新全部信息失败:', error);
    return {
      success: false,
      message: `批量更新全部信息失败: ${error}`,
      totalAccounts,
      successCount,
      failureCount,
      skippedCount,
      results
    };
  }
}

/**
 * 批量获取并更新信用分和粉丝数（仅信用分和粉丝数）
 */
export async function batchFetchAndUpdateCreditAndFans(db: D1Database): Promise<{
  success: boolean;
  message: string;
  totalAccounts: number;
  successCount: number;
  failureCount: number;
  skippedCount: number;
  results: FetchResult[];
}> {
  const startTime = new Date();
  console.log(`\n=== 开始批量更新信用分和粉丝数 (${startTime.toISOString()}) ===`);

  let totalAccounts = 0;
  let successCount = 0;
  let failureCount = 0;
  let skippedCount = 0;
  const results: FetchResult[] = [];

  try {
    // 获取所有主账号的平台数据
    console.log('查询数据库中的主账号平台数据...');
    const allMainAccountsData = await db.prepare(`
      SELECT main_account_id, platform_accounts_data FROM main_account_platform_data
    `).all<{ main_account_id: number; platform_accounts_data: string }>();

    if (!allMainAccountsData.results || allMainAccountsData.results.length === 0) {
      console.log('没有找到主账号平台数据');
      return {
        success: true,
        message: '没有找到需要更新的平台账号',
        totalAccounts: 0,
        successCount: 0,
        failureCount: 0,
        skippedCount: 0,
        results: []
      };
    }

    // 收集所有需要更新的账号
    const allAccounts: Array<{
      phone: string;
      mainAccountId: number;
      accountData: PlatformAccountData;
    }> = [];

    for (const mainAccountData of allMainAccountsData.results) {
      try {
        const platformData = validatePlatformAccountsCollection(mainAccountData.platform_accounts_data);
        if (platformData && platformData.accounts) {
          for (const [phone, accountData] of Object.entries(platformData.accounts)) {
            if (accountData.sessionid) {
              allAccounts.push({
                phone,
                mainAccountId: mainAccountData.main_account_id,
                accountData
              });
            }
          }
        }
      } catch (error) {
        console.error(`解析主账号 ${mainAccountData.main_account_id} 的平台数据失败:`, error);
      }
    }

    totalAccounts = allAccounts.length;
    console.log(`总共需要更新 ${totalAccounts} 个平台账号的信用分和粉丝数`);

    if (totalAccounts === 0) {
      return {
        success: true,
        message: '没有找到需要更新的平台账号',
        totalAccounts: 0,
        successCount: 0,
        failureCount: 0,
        skippedCount: 0,
        results: []
      };
    }

    // 批量处理所有账号（仅获取信用分和粉丝数）
    for (let i = 0; i < allAccounts.length; i++) {
      const account = allAccounts[i];
      console.log(`\n[${i + 1}/${totalAccounts}] 更新账号 ${account.phone} 的信用分和粉丝数...`);

      try {
        // 仅获取信用分和粉丝数
        const fetchResult = await fetchCreditAndFansOnly(account.phone, account.accountData.sessionid);

        if (fetchResult.success && fetchResult.data) {
          // 更新数据库
          try {
            const updateResult = await updateSinglePlatformAccount(
              db,
              account.mainAccountId,
              account.phone,
              fetchResult.data
            );

            if (updateResult.success) {
              successCount++;
              console.log(`  ✅ 账号 ${account.phone} 信用分和粉丝数更新成功`);
            } else {
              failureCount++;
              console.log(`  ❌ 账号 ${account.phone} 数据库更新失败: ${updateResult.message}`);

              // 更新结果记录
              results[results.length - 1] = {
                success: false,
                phone: account.phone,
                message: `数据库更新失败: ${updateResult.message}`,
                data: fetchResult.data
              };
            }
          } catch (updateError) {
            failureCount++;
            console.error(`  ❌ 账号 ${account.phone} 数据库更新异常:`, updateError);

            // 更新结果记录
            results[results.length - 1] = {
              success: false,
              phone: account.phone,
              message: `数据库更新异常: ${updateError}`,
              data: fetchResult.data
            };
          }
        } else {
          failureCount++;
          console.log(`  ❌ 账号 ${account.phone} 信用分和粉丝数获取失败: ${fetchResult.message}`);
        }

        results.push(fetchResult);

      } catch (error) {
        failureCount++;
        const errorMessage = `更新异常: ${error}`;
        console.error(`  ❌ 账号 ${account.phone} 更新异常:`, error);

        results.push({
          success: false,
          phone: account.phone,
          message: errorMessage
        });
      }
    }

    console.log(`\n=== 信用分和粉丝数更新完成 ===`);

    const message = `信用分和粉丝数更新完成: 总计${totalAccounts}个账号，成功${successCount}个，失败${failureCount}个，跳过${skippedCount}个`;

    return {
      success: true,
      message,
      totalAccounts,
      successCount,
      failureCount,
      skippedCount,
      results
    };

  } catch (error) {
    console.error('批量更新信用分和粉丝数失败:', error);
    return {
      success: false,
      message: `批量更新信用分和粉丝数失败: ${error}`,
      totalAccounts,
      successCount,
      failureCount,
      skippedCount,
      results
    };
  }
}

/**
 * 批量获取并更新账号详细信息（仅账号详细信息）
 */
export async function batchFetchAndUpdateAccountInfo(db: D1Database): Promise<{
  success: boolean;
  message: string;
  totalAccounts: number;
  successCount: number;
  failureCount: number;
  skippedCount: number;
  results: FetchResult[];
}> {
  const startTime = new Date();
  console.log(`\n=== 开始批量更新账号详细信息 (${startTime.toISOString()}) ===`);

  let totalAccounts = 0;
  let successCount = 0;
  let failureCount = 0;
  let skippedCount = 0;
  const results: FetchResult[] = [];

  try {
    // 获取所有主账号的平台数据
    console.log('查询数据库中的主账号平台数据...');
    const allMainAccountsData = await db.prepare(`
      SELECT main_account_id, platform_accounts_data FROM main_account_platform_data
    `).all<{ main_account_id: number; platform_accounts_data: string }>();

    if (!allMainAccountsData.results || allMainAccountsData.results.length === 0) {
      console.log('没有找到主账号平台数据');
      return {
        success: true,
        message: '没有找到需要更新的平台账号',
        totalAccounts: 0,
        successCount: 0,
        failureCount: 0,
        skippedCount: 0,
        results: []
      };
    }

    // 收集所有需要更新的账号
    const allAccounts: Array<{
      phone: string;
      mainAccountId: number;
      accountData: PlatformAccountData;
    }> = [];

    for (const mainAccountData of allMainAccountsData.results) {
      try {
        const platformData = validatePlatformAccountsCollection(mainAccountData.platform_accounts_data);
        if (platformData && platformData.accounts) {
          for (const [phone, accountData] of Object.entries(platformData.accounts)) {
            if (accountData.sessionid) {
              allAccounts.push({
                phone,
                mainAccountId: mainAccountData.main_account_id,
                accountData
              });
            }
          }
        }
      } catch (error) {
        console.error(`解析主账号 ${mainAccountData.main_account_id} 的平台数据失败:`, error);
      }
    }

    totalAccounts = allAccounts.length;
    console.log(`总共需要更新 ${totalAccounts} 个平台账号的详细信息`);

    if (totalAccounts === 0) {
      return {
        success: true,
        message: '没有找到需要更新的平台账号',
        totalAccounts: 0,
        successCount: 0,
        failureCount: 0,
        skippedCount: 0,
        results: []
      };
    }

    // 批量处理所有账号（仅获取账号详细信息）
    for (let i = 0; i < allAccounts.length; i++) {
      const account = allAccounts[i];
      console.log(`\n[${i + 1}/${totalAccounts}] 更新账号 ${account.phone} 的详细信息...`);

      try {
        // 仅获取账号详细信息
        const fetchResult = await fetchAccountInfoOnly(account.phone, account.accountData.sessionid);

        if (fetchResult.success && fetchResult.data) {
          // 更新数据库
          try {
            const updateResult = await updateSinglePlatformAccount(
              db,
              account.mainAccountId,
              account.phone,
              fetchResult.data
            );

            if (updateResult.success) {
              successCount++;
              console.log(`  ✅ 账号 ${account.phone} 详细信息更新成功`);
            } else {
              failureCount++;
              console.log(`  ❌ 账号 ${account.phone} 数据库更新失败: ${updateResult.message}`);

              // 更新结果记录
              results[results.length - 1] = {
                success: false,
                phone: account.phone,
                message: `数据库更新失败: ${updateResult.message}`,
                data: fetchResult.data
              };
            }
          } catch (updateError) {
            failureCount++;
            console.error(`  ❌ 账号 ${account.phone} 数据库更新异常:`, updateError);

            // 更新结果记录
            results[results.length - 1] = {
              success: false,
              phone: account.phone,
              message: `数据库更新异常: ${updateError}`,
              data: fetchResult.data
            };
          }
        } else {
          failureCount++;
          console.log(`  ❌ 账号 ${account.phone} 详细信息获取失败: ${fetchResult.message}`);
        }

        results.push(fetchResult);

      } catch (error) {
        failureCount++;
        const errorMessage = `更新异常: ${error}`;
        console.error(`  ❌ 账号 ${account.phone} 更新异常:`, error);

        results.push({
          success: false,
          phone: account.phone,
          message: errorMessage
        });
      }
    }

    console.log(`\n=== 账号详细信息更新完成 ===`);

    const message = `账号详细信息更新完成: 总计${totalAccounts}个账号，成功${successCount}个，失败${failureCount}个，跳过${skippedCount}个`;

    return {
      success: true,
      message,
      totalAccounts,
      successCount,
      failureCount,
      skippedCount,
      results
    };

  } catch (error) {
    console.error('批量更新账号详细信息失败:', error);
    return {
      success: false,
      message: `批量更新账号详细信息失败: ${error}`,
      totalAccounts,
      successCount,
      failureCount,
      skippedCount,
      results
    };
  }
}

/**
 * 仅获取账号状态（禁言状态）
 */
async function fetchAccountStatusOnly(phone: string, sessionid: string): Promise<FetchResult> {
  const result: FetchResult = {
    success: false,
    phone,
    message: '',
    data: {}
  };

  try {
    console.log(`  开始获取账号 ${phone} 的状态信息...`);
    console.log(`  sessionid前8位: ${sessionid.substring(0, 8)}...`);

    // 先获取账号详细信息以获取用户ID
    const accountInfoResponse = await fetchAccountInfo(sessionid);
    let userId: string | undefined;

    if (accountInfoResponse.user_info) {
      userId = accountInfoResponse.user_info.id;
      console.log(`  获取到用户ID: ${userId}`);
    } else {
      result.message = '无法获取用户ID';
      console.log(`  ❌ 无法获取用户ID`);
      return result;
    }

    // 获取禁言状态
    if (userId) {
      try {
        console.log(`  开始获取用户 ${userId} 的禁言状态...`);
        const punishResponse = await fetchPunishStatus(userId, sessionid);
        const accountStatus = parsePunishStatus(punishResponse);

        result.data!.account_status = accountStatus;
        console.log(`  禁言状态获取成功: ${accountStatus}`);

        result.success = true;
        result.message = '账号状态获取成功';
        console.log(`  ✅ 账号状态获取成功: ${accountStatus}`);
      } catch (punishError) {
        result.message = `禁言状态获取失败: ${punishError}`;
        console.log(`  ❌ 禁言状态获取失败:`, punishError);
      }
    }

  } catch (error) {
    result.message = `获取账号状态时发生异常: ${error}`;
    console.error(`  ❌ 获取账号状态时发生异常:`, error);
  }

  return result;
}

/**
 * 批量获取并更新账号状态（仅禁言状态）
 */
export async function batchFetchAndUpdateAccountStatus(db: D1Database): Promise<{
  success: boolean;
  message: string;
  totalAccounts: number;
  successCount: number;
  failureCount: number;
  skippedCount: number;
  results: FetchResult[];
}> {
  const startTime = new Date();
  console.log(`\n=== 开始批量更新账号状态 (${startTime.toISOString()}) ===`);

  let totalAccounts = 0;
  let successCount = 0;
  let failureCount = 0;
  let skippedCount = 0;
  const results: FetchResult[] = [];

  try {
    // 获取所有主账号的平台数据
    console.log('查询数据库中的主账号平台数据...');
    const allMainAccountsData = await db.prepare(`
      SELECT main_account_id, platform_accounts_data FROM main_account_platform_data
    `).all<{ main_account_id: number; platform_accounts_data: string }>();

    if (!allMainAccountsData.results || allMainAccountsData.results.length === 0) {
      console.log('没有找到主账号平台数据');
      return {
        success: true,
        message: '没有找到需要更新的平台账号',
        totalAccounts: 0,
        successCount: 0,
        failureCount: 0,
        skippedCount: 0,
        results: []
      };
    }

    // 收集所有需要更新的账号
    const allAccounts: Array<{
      phone: string;
      mainAccountId: number;
      accountData: PlatformAccountData;
    }> = [];

    for (const mainAccountData of allMainAccountsData.results) {
      try {
        const platformData = validatePlatformAccountsCollection(mainAccountData.platform_accounts_data);
        if (platformData && platformData.accounts) {
          for (const [phone, accountData] of Object.entries(platformData.accounts)) {
            if (accountData.sessionid) {
              allAccounts.push({
                phone,
                mainAccountId: mainAccountData.main_account_id,
                accountData
              });
            }
          }
        }
      } catch (error) {
        console.error(`解析主账号 ${mainAccountData.main_account_id} 的平台数据失败:`, error);
      }
    }

    totalAccounts = allAccounts.length;
    console.log(`总共需要更新 ${totalAccounts} 个平台账号的状态`);

    if (totalAccounts === 0) {
      return {
        success: true,
        message: '没有找到需要更新的平台账号',
        totalAccounts: 0,
        successCount: 0,
        failureCount: 0,
        skippedCount: 0,
        results: []
      };
    }

    // 批量处理所有账号（仅获取账号状态）
    for (let i = 0; i < allAccounts.length; i++) {
      const account = allAccounts[i];
      console.log(`\n[${i + 1}/${totalAccounts}] 更新账号 ${account.phone} 的状态...`);

      try {
        // 仅获取账号状态
        const fetchResult = await fetchAccountStatusOnly(account.phone, account.accountData.sessionid);

        if (fetchResult.success && fetchResult.data) {
          // 更新数据库
          try {
            const updateResult = await updateSinglePlatformAccount(
              db,
              account.mainAccountId,
              account.phone,
              fetchResult.data
            );

            if (updateResult.success) {
              successCount++;
              console.log(`  ✅ 账号 ${account.phone} 状态更新成功`);
            } else {
              failureCount++;
              console.log(`  ❌ 账号 ${account.phone} 数据库更新失败: ${updateResult.message}`);

              // 更新结果记录
              results[results.length - 1] = {
                success: false,
                phone: account.phone,
                message: `数据库更新失败: ${updateResult.message}`,
                data: fetchResult.data
              };
            }
          } catch (updateError) {
            failureCount++;
            console.error(`  ❌ 账号 ${account.phone} 数据库更新异常:`, updateError);

            // 更新结果记录
            results[results.length - 1] = {
              success: false,
              phone: account.phone,
              message: `数据库更新异常: ${updateError}`,
              data: fetchResult.data
            };
          }
        } else {
          failureCount++;
          console.log(`  ❌ 账号 ${account.phone} 状态获取失败: ${fetchResult.message}`);
        }

        results.push(fetchResult);

      } catch (error) {
        failureCount++;
        const errorMessage = `更新异常: ${error}`;
        console.error(`  ❌ 账号 ${account.phone} 更新异常:`, error);

        results.push({
          success: false,
          phone: account.phone,
          message: errorMessage
        });
      }
    }

    console.log(`\n=== 账号状态更新完成 ===`);

    const message = `账号状态更新完成: 总计${totalAccounts}个账号，成功${successCount}个，失败${failureCount}个，跳过${skippedCount}个`;

    return {
      success: true,
      message,
      totalAccounts,
      successCount,
      failureCount,
      skippedCount,
      results
    };

  } catch (error) {
    console.error('批量更新账号状态失败:', error);
    return {
      success: false,
      message: `批量更新账号状态失败: ${error}`,
      totalAccounts,
      successCount,
      failureCount,
      skippedCount,
      results
    };
  }
}

/**
 * 批量获取并更新收益数据（仅收益）
 */
export async function batchFetchAndUpdateIncomeOnly(db: D1Database): Promise<{
  success: boolean;
  message: string;
  totalAccounts: number;
  successCount: number;
  failureCount: number;
  skippedCount: number;
  results: FetchResult[];
}> {
  const startTime = new Date();
  console.log(`\n=== 开始批量更新收益数据 (${startTime.toISOString()}) ===`);

  let totalAccounts = 0;
  let successCount = 0;
  let failureCount = 0;
  let skippedCount = 0;
  const results: FetchResult[] = [];

  try {
    // 获取所有主账号的平台数据
    console.log('查询数据库中的主账号平台数据...');
    const allMainAccountsData = await db.prepare(`
      SELECT main_account_id, platform_accounts_data FROM main_account_platform_data
    `).all<{ main_account_id: number; platform_accounts_data: string }>();

    if (!allMainAccountsData.results || allMainAccountsData.results.length === 0) {
      console.log('没有找到主账号平台数据');
      return {
        success: true,
        message: '没有找到需要更新的平台账号',
        totalAccounts: 0,
        successCount: 0,
        failureCount: 0,
        skippedCount: 0,
        results: []
      };
    }

    // 收集所有需要更新的账号
    const allAccounts: Array<{
      phone: string;
      mainAccountId: number;
      accountData: PlatformAccountData;
    }> = [];

    for (const mainAccountData of allMainAccountsData.results) {
      try {
        const platformData = validatePlatformAccountsCollection(mainAccountData.platform_accounts_data);
        if (platformData && platformData.accounts) {
          for (const [phone, accountData] of Object.entries(platformData.accounts)) {
            if (accountData.sessionid) {
              allAccounts.push({
                phone,
                mainAccountId: mainAccountData.main_account_id,
                accountData
              });
            }
          }
        }
      } catch (error) {
        console.error(`解析主账号 ${mainAccountData.main_account_id} 的平台数据失败:`, error);
      }
    }

    totalAccounts = allAccounts.length;
    console.log(`总共需要更新 ${totalAccounts} 个平台账号的收益数据`);

    if (totalAccounts === 0) {
      return {
        success: true,
        message: '没有找到需要更新的平台账号',
        totalAccounts: 0,
        successCount: 0,
        failureCount: 0,
        skippedCount: 0,
        results: []
      };
    }

    // 批量处理所有账号（仅获取收益数据）
    for (let i = 0; i < allAccounts.length; i++) {
      const account = allAccounts[i];
      console.log(`\n[${i + 1}/${totalAccounts}] 更新账号 ${account.phone} 的收益数据...`);

      try {
        // 仅获取收益数据
        const fetchResult = await fetchIncomeDataOnly(account.phone, account.accountData.sessionid);

        if (fetchResult.success && fetchResult.data) {
          // 更新数据库
          try {
            const updateResult = await updateSinglePlatformAccount(
              db,
              account.mainAccountId,
              account.phone,
              fetchResult.data
            );

            if (updateResult.success) {
              successCount++;
              console.log(`  ✅ 账号 ${account.phone} 收益数据更新成功`);
              console.log(`    更新的数据: 总收益=${fetchResult.data.total_income}, 昨日收益=${fetchResult.data.yesterday_income}, 可提现=${fetchResult.data.can_withdraw_amount}`);
            } else {
              failureCount++;
              console.log(`  ❌ 账号 ${account.phone} 数据库更新失败: ${updateResult.message}`);

              // 更新结果记录
              results[results.length - 1] = {
                success: false,
                phone: account.phone,
                message: `数据库更新失败: ${updateResult.message}`,
                data: fetchResult.data
              };
            }
          } catch (updateError) {
            failureCount++;
            console.error(`  ❌ 账号 ${account.phone} 数据库更新异常:`, updateError);

            // 更新结果记录
            results[results.length - 1] = {
              success: false,
              phone: account.phone,
              message: `数据库更新异常: ${updateError}`,
              data: fetchResult.data
            };
          }
        } else {
          failureCount++;
          console.log(`  ❌ 账号 ${account.phone} 收益数据获取失败: ${fetchResult.message}`);
        }

        results.push(fetchResult);

      } catch (error) {
        failureCount++;
        const errorMessage = `更新异常: ${error}`;
        console.error(`  ❌ 账号 ${account.phone} 更新异常:`, error);

        results.push({
          success: false,
          phone: account.phone,
          message: errorMessage
        });
      }
    }

    console.log(`\n=== 收益数据更新完成 ===`);

    const endTime = new Date();
    const message = `收益数据更新完成: 总计${totalAccounts}个账号，成功${successCount}个，失败${failureCount}个，跳过${skippedCount}个`;

    return {
      success: true,
      message,
      totalAccounts,
      successCount,
      failureCount,
      skippedCount,
      results
    };

  } catch (error) {
    console.error('批量更新收益数据失败:', error);
    return {
      success: false,
      message: `批量更新收益数据失败: ${error}`,
      totalAccounts,
      successCount,
      failureCount,
      skippedCount,
      results
    };
  }
}

